<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> mề<PERSON><PERSON> l<PERSON> đồng <PERSON> động</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/react@17.0.2/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@17.0.2/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.18.13/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/docx@7.8.2/build/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            background-color: #f9fafb;
        }
        .custom-rounded {
            border-radius: 30px;
        }
        .editor-container {
            min-height: 80vh;
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
            background-color: white;
        }
        .editor-container:focus {
            outline: none;
            border-color: #3b82f6;
        }
        .contract-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header-columns {
            display: flex;
            justify-content: space-between;
        }
        .header-left {
            text-align: center;
            width: 50%;
        }
        .header-right {
            text-align: center;
            width: 50%;
        }
        .contract-title {
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            margin: 15px 0;
        }
        .contract-parties {
            margin-bottom: 15px;
        }
        .contract-article {
            margin-bottom: 15px;
        }
        .contract-article-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        .signature-block {
            text-align: center;
            width: 45%;
        }
        .bullet-list {
            list-style-type: disc;
            margin-left: 20px;
        }
        .bullet-list li {
            margin-bottom: 5px;
        }
        .editable-field {
            border-bottom: 1px dashed #ccc;
            padding: 2px 5px;
            min-width: 100px;
            display: inline-block;
        }
        .editable-field:hover, .editable-field:focus {
            background-color: #f0f9ff;
            border-bottom: 1px dashed #3b82f6;
            outline: none;
        }
        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        .toolbar-button {
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
        }
        .toolbar-button:hover {
            background-color: #f3f4f6;
        }
        .toolbar-button.active {
            background-color: #dbeafe;
            border-color: #93c5fd;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                background-color: white;
            }
            .editor-container {
                border: none;
                min-height: auto;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // React Components
        const { useState, useEffect, useRef } = React;
        const { Document, Paragraph, TextRun, HeadingLevel, AlignmentType, UnderlineType, BorderStyle, WidthType, TableCell, TableRow, Table } = docx;

        // EditableField Component for inline editing
        const EditableField = ({ value, onChange, className }) => {
            return (
                <span 
                    className={`editable-field ${className || ''}`}
                    contentEditable={true}
                    onBlur={(e) => onChange(e.target.textContent)}
                    suppressContentEditableWarning={true}
                >
                    {value}
                </span>
            );
        };

        // Main App Component
        const App = () => {
            const [contractData, setContractData] = useState({
                contractNumber: '01/2023/AIT-HĐLĐ',
                companyName: 'CÔNG TY CP TM QUỐC TẾ TÂN ĐẠI TÂY DƯƠNG',
                employerName: 'Nguyễn Bảo Thi',
                employerPosition: 'Giám đốc',
                employerNationality: 'Việt Nam',
                companyAddress: 'A60 Đường Phú Thuận, Phường Phú Thuận, Quận 7, TP.HCM',
                employeeName: 'Nguyễn Như Nam',
                employeeGender: 'Ông', 
                employeeNationality: 'Việt Nam',
                employeeBirthday: '13/11/2003',
                employeeSkill: 'Lập trình viên',
                employeeAddress: 'Quận 7, TP.HCM',
                employeeCurrentAddress: 'Quận 7, TP.HCM',
                employeeID: '0123456789',
                employeeIDDate: '22/11/2023',
                employeeIDPlace: 'Cục quản lý hành chính về trật tự xã hội',
                contractType: '12 tháng',
                contractStartDate: '01 tháng 01 năm 2024',
                contractEndDate: '01 tháng 01 năm 2025',
                workLocation: 'Văn phòng Công ty',
                workAddress: 'A60 Đường Phú Thuận, Phường Phú Thuận, Quận 7, TP.HCM',
                employeePosition: 'Nhân viên',
                workDescription: 'Theo sự phân công trực tiếp của cán bộ quản lý',
                workingTime: 'Theo công việc',
                workingTools: 'Tùy theo công việc',
                transportation: 'Tự túc',
                baseSalary: '5.100.000/tháng (26 công)',
                paymentMethod: 'Tiền mặt hoặc chuyển khoản',
                allowance: 'Theo quy chế Công ty',
                paymentTime: 'Từ ngày 10 đến ngày 15 hàng tháng',
                bonus: 'Theo quy chế của Công ty',
                salaryIncrease: 'Theo quy chế của Công ty',
                restTime: 'Theo luật lao động',
                insurance: 'Theo quy định',
                training: '',
                otherAgreements: 'Làm thêm giờ khi có yêu cầu và được hưởng phụ cấp tăng giờ (nếu có) theo quy định',
                signatureName: 'NGUYỄN NHƯ NAM', 
            });

            // Update a specific field in the contract data
            const updateField = (field, value) => {
                setContractData({
                    ...contractData,
                    [field]: value
                });
            };

            // Set signature name when employee name changes
            useEffect(() => {
                if (contractData.employeeName) {
                    updateField('signatureName', contractData.employeeName.toUpperCase());
                }
            }, []);

            // Handle export to Word
            const exportToWord = () => {
                const { 
                    Document, Paragraph, TextRun, HeadingLevel, AlignmentType, 
                    UnderlineType, BorderStyle, WidthType, TableCell, TableRow, Table, 
                    VerticalAlign, Tab, NumberFormat
                } = docx;

                // Create centered header with company and country names
                const headerTable = new Table({
                    width: {
                        size: 100,
                        type: WidthType.PERCENTAGE,
                    },
                    borders: {
                        top: { style: BorderStyle.NONE },
                        bottom: { style: BorderStyle.NONE },
                        left: { style: BorderStyle.NONE },
                        right: { style: BorderStyle.NONE },
                        insideHorizontal: { style: BorderStyle.NONE },
                        insideVertical: { style: BorderStyle.NONE },
                    },
                    rows: [
                        new TableRow({
                            children: [
                                new TableCell({
                                    width: {
                                        size: 50,
                                        type: WidthType.PERCENTAGE,
                                    },
                                    children: [
                                        new Paragraph({
                                            text: 'CÔNG TY CP TM QUỐC TẾ',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: 'TÂN ĐẠI TÂY DƯƠNG',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: '_______',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                    ],
                                }),
                                new TableCell({
                                    width: {
                                        size: 50,
                                        type: WidthType.PERCENTAGE,
                                    },
                                    children: [
                                        new Paragraph({
                                            text: 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: 'Độc lập – Tự do – Hạnh phúc',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: '_________________________',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                    ],
                                }),
                            ],
                        }),
                    ],
                });

                // Helper function to create bullet points
                const createBulletPoint = (text) => {
                    return new Paragraph({
                        text: text,
                        bullet: {
                            level: 0
                        },
                        indent: {
                            left: 720  // Indentation for bullet points (in twips)
                        }
                    });
                };

                // Main document sections
                const mainDocumentContent = [
                    // Contract number
                    new Paragraph({
                        text: `Số: ${contractData.contractNumber}`,
                        alignment: AlignmentType.CENTER,
                        spacing: {
                            after: 200
                        }
                    }),

                    // Contract title
                    new Paragraph({
                        text: 'HỢP ĐỒNG LAO ĐỘNG',
                        heading: HeadingLevel.HEADING_1,
                        alignment: AlignmentType.CENTER,
                        spacing: {
                            before: 200,
                            after: 200
                        }
                    }),

                    // Sub title
                    new Paragraph({
                        text: '(Ban hành theo thông tư số 23/2015/TT-BLĐTBXH ngày 23/06/2015 của',
                        alignment: AlignmentType.CENTER,
                    }),
                    new Paragraph({
                        text: 'Bộ lao động thương binh và xã hội)',
                        alignment: AlignmentType.CENTER,
                        spacing: {
                            after: 400
                        }
                    }),

                    // Contract parties
                    new Paragraph({
                        children: [
                            new TextRun('Chúng tôi, một bên là Ông: '),
                            new TextRun({
                                text: contractData.employerName,
                                bold: true
                            }),
                            new TextRun('\t\t\tQuốc tịch: '),
                            new TextRun({
                                text: contractData.employerNationality,
                                bold: true
                            }),
                        ],
                        spacing: {
                            after: 200
                        }
                    }),

                    new Paragraph({ text: `Chức vụ: ${contractData.employerPosition}` }),
                    new Paragraph({ text: `Đại diện cho: ${contractData.companyName}` }),
                    new Paragraph({ 
                        text: `Địa chỉ: ${contractData.companyAddress}`,
                        spacing: {
                            after: 200
                        }
                    }),

                    new Paragraph({
                        children: [
                            new TextRun(`Và một bên là ${contractData.employeeGender} `),
                            new TextRun({
                                text: contractData.employeeName,
                                bold: true
                            }),
                            new TextRun('\t\t\t\tQuốc tịch: '),
                            new TextRun({
                                text: contractData.employeeNationality,
                                bold: true
                            }),
                        ],
                        spacing: {
                            after: 200
                        }
                    }),

                    new Paragraph({ text: `Sinh ngày: ${contractData.employeeBirthday}` }),
                    new Paragraph({ text: `Chuyên môn: ${contractData.employeeSkill}` }),
                    new Paragraph({ text: `Địa chỉ thường trú: ${contractData.employeeAddress}` }),
                    new Paragraph({ text: `Chỗ ở hiện nay: ${contractData.employeeCurrentAddress}` }),
                    new Paragraph({ 
                        text: `Số CMND: ${contractData.employeeID}; Cấp ngày: ${contractData.employeeIDDate}; Nơi cấp: ${contractData.employeeIDPlace}`,
                        spacing: {
                            after: 200
                        }
                    }),

                    new Paragraph({
                        text: 'Thỏa thuận ký kết hợp đồng lao động và cam kết làm đúng những điều khoản sau đây:',
                        spacing: {
                            after: 400
                        }
                    }),

                    // Article 1
                    new Paragraph({
                        text: 'Điều 1: Thời hạn và công việc hợp đồng',
                        heading: HeadingLevel.HEADING_2,
                        spacing: {
                            before: 200,
                            after: 200
                        }
                    }),

                    createBulletPoint(`Loại hợp đồng lao động: ${contractData.contractType}`),
                    createBulletPoint(`Từ ngày ${contractData.contractStartDate} đến ngày ${contractData.contractEndDate}`),
                    createBulletPoint(`Địa điểm làm việc: ${contractData.workLocation}`),
                    createBulletPoint(`Địa chỉ: ${contractData.workAddress}`),
                    createBulletPoint(`Chức danh chuyên môn: ${contractData.employeePosition}`),
                    createBulletPoint(`Công việc phải làm: ${contractData.workDescription}`),

                    // Article 2
                    new Paragraph({
                        text: 'Điều 2: Chế độ làm việc',
                        heading: HeadingLevel.HEADING_2,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),

                    createBulletPoint(`Thời gian làm việc theo công việc: ${contractData.workingTime}`),
                    createBulletPoint(`Được cấp phát những dụng cụ làm việc gồm: ${contractData.workingTools}`),

                    // Article 3
                    new Paragraph({
                        text: 'Điều 3: Quyền lợi và nghĩa vụ của người lao động',
                        heading: HeadingLevel.HEADING_2,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),

                    new Paragraph({
                        text: '1. Quyền lợi:',
                        style: "Strong",
                        spacing: {
                            after: 200
                        }
                    }),

                    createBulletPoint(`Phương tiện đi lại làm việc: ${contractData.transportation}`),
                    createBulletPoint(`Mức lương chính hoặc tiền công: Lương cơ bản: ${contractData.baseSalary}, chưa bao gồm các khoản phụ cấp và hiệu quả công việc`),
                    createBulletPoint(`Hình thức trả lương: ${contractData.paymentMethod}`),
                    createBulletPoint(`Phụ cấp gồm: ${contractData.allowance}`),
                    createBulletPoint(`Thời gian trả lương: ${contractData.paymentTime}`),
                    createBulletPoint(`Tiền thưởng: ${contractData.bonus}`),
                    createBulletPoint(`Chế độ tăng lương: ${contractData.salaryIncrease}`),
                    createBulletPoint(`Chế độ nghỉ ngơi (nghỉ hàng tuần, phép năm, lễ, tết): ${contractData.restTime}`),
                    createBulletPoint(`Bảo hiểm xã hội và bảo hiểm y tế: ${contractData.insurance}`),
                    createBulletPoint(`Chế độ đào tạo: ${contractData.training}`),
                    createBulletPoint(`Những Thỏa thuận khác: ${contractData.otherAgreements}`),

                    new Paragraph({
                        text: '2. Nghĩa vụ:',
                        style: "Strong",
                        spacing: {
                            before: 200,
                            after: 200
                        }
                    }),

                    createBulletPoint('Hoàn thành công việc đã cam kết trong hợp đồng lao động.'),
                    createBulletPoint('Chấp hành lệnh điều hành sản xuất-kinh doanh, nội quy kỷ luật lao động, an toàn lao động …'),
                    createBulletPoint('Bồi thường lao động và vật chất: Theo thỏa thuận với chủ sở hữu tài sản hoặc theo quy định.'),

                    // Article 4
                    new Paragraph({
                        text: 'Điều 4: Nghĩa vụ và quyền hạn của người sử dụng lao động',
                        heading: HeadingLevel.HEADING_2,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),

                    new Paragraph({
                        text: '1. Nghĩa vụ:',
                        style: "Strong",
                        spacing: {
                            after: 200
                        }
                    }),

                    createBulletPoint('Bảo đảm việc làm và thực hiện đầy đủ những điều đã cam kết trong hợp đồng lao động.'),
                    createBulletPoint('Thanh toán đầy đủ, đúng thời hạn các chế độ và quyền lợi cho người lao động theo hợp đồng lao động, thỏa ước lao động (nếu có).'),

                    new Paragraph({
                        text: '2. Quyền hạn:',
                        style: "Strong",
                        spacing: {
                            before: 200,
                            after: 200
                        }
                    }),

                    createBulletPoint('Điều hành người lao động hoàn thành công việc theo hợp đồng (bố trí, điều chuyển, tạm ngừng việc …)'),
                    createBulletPoint('Tạm hoãn, chấm dứt hợp đồng lao động, kỷ luật người lao động theo quy định của pháp luật, thỏa ước lao động tập thể (nếu có) và nội quy lao động của doanh nghiệp.'),

                    // Article 5
                    new Paragraph({
                        text: 'Điều 5: Điều khoản thi hành',
                        heading: HeadingLevel.HEADING_2,
                        spacing: {
                            before: 400,
                            after: 200
                        }
                    }),

                    createBulletPoint('Những vấn đề về lao động, không ghi trong hợp đồng lao động này thì áp dụng quy định của thỏa ước lao động tập thể.'),
                    createBulletPoint(`Hợp đồng này được làm tại văn phòng Công ty, mỗi bên giữ một bản có giá trị pháp lý như nhau. Và có hiệu lực từ ngày ${contractData.contractStartDate}.`),
                ];

                // Create signature section
                const signatureTable = new Table({
                    width: {
                        size: 100,
                        type: WidthType.PERCENTAGE,
                    },
                    borders: {
                        top: { style: BorderStyle.NONE },
                        bottom: { style: BorderStyle.NONE },
                        left: { style: BorderStyle.NONE },
                        right: { style: BorderStyle.NONE },
                        insideHorizontal: { style: BorderStyle.NONE },
                        insideVertical: { style: BorderStyle.NONE },
                    },
                    rows: [
                        new TableRow({
                            children: [
                                new TableCell({
                                    width: {
                                        size: 50,
                                        type: WidthType.PERCENTAGE,
                                    },
                                    children: [
                                        new Paragraph({
                                            text: 'NGƯỜI LAO ĐỘNG',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: '(Ký tên)',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                        new Paragraph({
                                            text: 'Ghi rõ họ và tên',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                        new Paragraph({ text: '' }),
                                        new Paragraph({ text: '' }),
                                        new Paragraph({
                                            text: contractData.signatureName,
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                    ],
                                }),
                                new TableCell({
                                    width: {
                                        size: 50,
                                        type: WidthType.PERCENTAGE,
                                    },
                                    children: [
                                        new Paragraph({
                                            text: 'NGƯỜI SỬ DỤNG LAO ĐỘNG',
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                        new Paragraph({
                                            text: '(Ký tên, đóng dấu)',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                        new Paragraph({
                                            text: 'Ghi rõ họ và tên',
                                            alignment: AlignmentType.CENTER,
                                        }),
                                        new Paragraph({ text: '' }),
                                        new Paragraph({ text: '' }),
                                        new Paragraph({
                                            text: contractData.employerName.toUpperCase(),
                                            alignment: AlignmentType.CENTER,
                                            style: "Strong",
                                        }),
                                    ],
                                }),
                            ],
                        }),
                    ],
                });

                // Create styles
                const doc = new Document({
                    styles: {
                        paragraphStyles: [
                            {
                                id: "Strong",
                                name: "Strong",
                                run: {
                                    bold: true,
                                },
                            },
                        ],
                    },
                    sections: [
                        {
                            properties: {},
                            children: [
                                headerTable,
                                ...mainDocumentContent,
                                new Paragraph({ text: '', spacing: { before: 400 } }),
                                signatureTable,
                            ],
                        },
                    ],
                });

                // Export the document
                docx.Packer.toBlob(doc).then(blob => {
                    saveAs(blob, `HĐLĐ ${contractData.employeeName}.docx`);
                    showNotification('Xuất file Word thành công!');
                });
            };

            // Show temporary notification
            const showNotification = (message) => {
                const notification = document.createElement('div');
                notification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50';
                notification.textContent = message;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            };

            // Main render
            return (
                <div className="container mx-auto px-4 py-8">
                    <header className="flex flex-col md:flex-row items-start md:items-center md:justify-between mb-8 no-print">
                        <div>
                            <h1 className="text-3xl font-bold text-blue-700">Phần mềm Quản lý Hợp đồng Lao động</h1>
                            <p className="text-gray-600">Chỉnh sửa và xuất hợp đồng dễ dàng</p>
                        </div>
                        <div className="mt-4 md:mt-0">
                            <button 
                                onClick={exportToWord}
                                className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 custom-rounded"
                            >
                                Xuất file Word
                            </button>
                        </div>
                    </header>

                    <div className="bg-white rounded-lg shadow-lg p-6 mb-6 no-print">
                        <h2 className="text-xl font-bold text-blue-700 mb-4">Tùy chọn người lao động</h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="mb-4">
                                <label htmlFor="employeeGender" className="block text-gray-700 font-bold mb-2">Giới tính:</label>
                                <select 
                                    id="employeeGender" 
                                    value={contractData.employeeGender}
                                    onChange={(e) => updateField('employeeGender', e.target.value)}
                                    className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                >
                                    <option value="Ông">Ông</option>
                                    <option value="Bà">Bà</option>
                                </select>
                            </div>
                            <div className="mb-4">
                                <label htmlFor="employeeName" className="block text-gray-700 font-bold mb-2">Tên người lao động:</label>
                                <input 
                                    type="text" 
                                    id="employeeName" 
                                    value={contractData.employeeName}
                                    onChange={(e) => {
                                        updateField('employeeName', e.target.value);
                                    }}
                                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>
                            <div className="mb-4">
                                <label htmlFor="signatureName" className="block text-gray-700 font-bold mb-2">Tên ký trong hợp đồng:</label>
                                <input 
                                    type="text" 
                                    id="signatureName" 
                                    value={contractData.signatureName}
                                    onChange={(e) => updateField('signatureName', e.target.value)}
                                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <div className="mb-4 no-print">
                            <label htmlFor="contractName" className="block text-gray-700 font-bold mb-2">Tên hợp đồng:</label>
                            <input 
                                type="text" 
                                id="contractName" 
                                value={`HĐLĐ ${contractData.employeeName}`}
                                onChange={(e) => updateField('employeeName', e.target.value.replace('HĐLĐ ', ''))}
                                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            />
                        </div>

                        <div className="mb-4">
                            <label className="block text-gray-700 font-bold mb-2 no-print">Nội dung hợp đồng:</label>
                            <div className="editor-container">
                                <div className="contract-header">
                                    <div className="header-columns">
                                        <div className="header-left">
                                            <p><strong>CÔNG TY CP TM QUỐC TẾ</strong></p>
                                            <p><strong>TÂN ĐẠI TÂY DƯƠNG</strong></p>
                                            <p>_______</p>
                                        </div>
                                        <div className="header-right">
                                            <p><strong>CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</strong></p>
                                            <p><strong>Độc lập – Tự do – Hạnh phúc</strong></p>
                                            <p>_________________________</p>
                                        </div>
                                    </div>
                                    <p>Số: <EditableField value={contractData.contractNumber} onChange={(value) => updateField('contractNumber', value)} /></p>
                                </div>

                                <div className="contract-title">
                                    <p>HỢP ĐỒNG LAO ĐỘNG</p>
                                    <p>( Ban hành theo thông tư số 23/2015/TT-BLĐTBXH ngày 23/06/2015 của</p>
                                    <p>Bộ lao động thương binh và xã hội )</p>
                                </div>

                                <div className="contract-parties">
                                    <p>Chúng tôi, một bên là Ông: <EditableField value={contractData.employerName} onChange={(value) => updateField('employerName', value)} />
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Quốc tịch: <EditableField value={contractData.employerNationality} onChange={(value) => updateField('employerNationality', value)} />
                                    </p>
                                    <p>Chức vụ: <EditableField value={contractData.employerPosition} onChange={(value) => updateField('employerPosition', value)} /></p>
                                    <p>Đại diện cho: <EditableField value={contractData.companyName} onChange={(value) => updateField('companyName', value)} /></p>
                                    <p>Địa chỉ: <EditableField value={contractData.companyAddress} onChange={(value) => updateField('companyAddress', value)} /></p>
                                    <p>Và một bên là {contractData.employeeGender} <EditableField value={contractData.employeeName} onChange={(value) => updateField('employeeName', value)} />
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Quốc tịch: <EditableField value={contractData.employeeNationality} onChange={(value) => updateField('employeeNationality', value)} />
                                    </p>
                                    <p>Sinh ngày: <EditableField value={contractData.employeeBirthday} onChange={(value) => updateField('employeeBirthday', value)} /></p>
                                    <p>Chuyên môn: <EditableField value={contractData.employeeSkill} onChange={(value) => updateField('employeeSkill', value)} /></p>
                                    <p>Địa chỉ thường trú: <EditableField value={contractData.employeeAddress} onChange={(value) => updateField('employeeAddress', value)} /></p>
                                    <p>Chỗ ở hiện nay: <EditableField value={contractData.employeeCurrentAddress} onChange={(value) => updateField('employeeCurrentAddress', value)} /></p>
                                    <p>Số CMND: <EditableField value={contractData.employeeID} onChange={(value) => updateField('employeeID', value)} /> ; 
                                       Cấp ngày: <EditableField value={contractData.employeeIDDate} onChange={(value) => updateField('employeeIDDate', value)} /> ; 
                                       Nơi cấp: <EditableField value={contractData.employeeIDPlace} onChange={(value) => updateField('employeeIDPlace', value)} />
                                    </p>
                                    <p>Thỏa thuận ký kết hợp đồng lao động và cam kết làm đúng những điều khoản sau đây:</p>
                                </div>

                                <div className="contract-article">
                                    <p className="contract-article-title">Điều 1: Thời hạn và công việc hợp đồng</p>
                                    <ul className="bullet-list">
                                        <li>Loại hợp đồng lao động: <EditableField value={contractData.contractType} onChange={(value) => updateField('contractType', value)} /></li>
                                        <li>Từ ngày <EditableField value={contractData.contractStartDate} onChange={(value) => updateField('contractStartDate', value)} /> 
                                            đến ngày <EditableField value={contractData.contractEndDate} onChange={(value) => updateField('contractEndDate', value)} />
                                        </li>
                                        <li>Địa điểm làm việc: <EditableField value={contractData.workLocation} onChange={(value) => updateField('workLocation', value)} /></li>
                                        <li>Địa chỉ: <EditableField value={contractData.workAddress} onChange={(value) => updateField('workAddress', value)} /></li>
                                        <li>Chức danh chuyên môn: <EditableField value={contractData.employeePosition} onChange={(value) => updateField('employeePosition', value)} /></li>
                                        <li>Công việc phải làm: <EditableField value={contractData.workDescription} onChange={(value) => updateField('workDescription', value)} /></li>
                                    </ul>
                                </div>

                                <div className="contract-article">
                                    <p className="contract-article-title">Điều 2: Chế độ làm việc:</p>
                                    <ul className="bullet-list">
                                        <li>Thời gian làm việc theo công việc: <EditableField value={contractData.workingTime} onChange={(value) => updateField('workingTime', value)} /></li>
                                        <li>Được cấp phát những dụng cụ làm việc gồm: <EditableField value={contractData.workingTools} onChange={(value) => updateField('workingTools', value)} /></li>
                                    </ul>
                                </div>

                                <div className="contract-article">
                                    <p className="contract-article-title">Điều 3: Quyền lợi và nghĩa vụ của người lao động</p>
                                    <p><strong>1. Quyền lợi:</strong></p>
                                    <ul className="bullet-list">
                                        <li>Phương tiện đi lại làm việc: <EditableField value={contractData.transportation} onChange={(value) => updateField('transportation', value)} /></li>
                                        <li>Mức lương chính hoặc tiền công: Lương cơ bản: <EditableField value={contractData.baseSalary} onChange={(value) => updateField('baseSalary', value)} />, 
                                            chưa bao gồm các khoản phụ cấp và hiệu quả công việc
                                        </li>
                                        <li>Hình thức trả lương: <EditableField value={contractData.paymentMethod} onChange={(value) => updateField('paymentMethod', value)} /></li>
                                        <li>Phụ cấp gồm: <EditableField value={contractData.allowance} onChange={(value) => updateField('allowance', value)} /></li>
                                        <li>Thời gian trả lương: <EditableField value={contractData.paymentTime} onChange={(value) => updateField('paymentTime', value)} /></li>
                                        <li>Tiền thưởng: <EditableField value={contractData.bonus} onChange={(value) => updateField('bonus', value)} /></li>
                                        <li>Chế độ tăng lương: <EditableField value={contractData.salaryIncrease} onChange={(value) => updateField('salaryIncrease', value)} /></li>
                                        <li>Chế độ nghỉ ngơi (nghỉ hàng tuần, phép năm, lễ, tết): <EditableField value={contractData.restTime} onChange={(value) => updateField('restTime', value)} /></li>
                                        <li>Bảo hiểm xã hội và bảo hiểm y tế: <EditableField value={contractData.insurance} onChange={(value) => updateField('insurance', value)} /></li>
                                        <li>Chế độ đào tạo: <EditableField value={contractData.training} onChange={(value) => updateField('training', value)} /></li>
                                        <li>Những Thỏa thuận khác: <EditableField value={contractData.otherAgreements} onChange={(value) => updateField('otherAgreements', value)} /></li>
                                    </ul>
                                    <p><strong>2. Nghĩa vụ:</strong></p>
                                    <ul className="bullet-list">
                                        <li>Hoàn thành công việc đã cam kết trong hợp đồng lao động.</li>
                                        <li>Chấp hành lệnh điều hành sản xuất-kinh doanh, nội quy kỷ luật lao động, an toàn lao động …</li>
                                        <li>Bồi thường lao động và vật chất: Theo thỏa thuận với chủ sở hữu tài sản hoặc theo quy định.</li>
                                    </ul>
                                </div>

                                <div className="contract-article">
                                    <p className="contract-article-title">Điều 4: Nghĩa vụ và quyền hạn của người sử dụng lao động:</p>
                                    <p><strong>1. Nghĩa vụ:</strong></p>
                                    <ul className="bullet-list">
                                        <li>Bảo đảm việc làm và thực hiện đầy đủ những điều đã cam kết trong hợp đồng lao động.</li>
                                        <li>Thanh toán đầy đủ, đúng thời hạn các chế độ và quyền lợi cho người lao động theo hợp đồng lao động, thỏa ước lao động (nếu có).</li>
                                    </ul>
                                    <p><strong>2. Quyền hạn:</strong></p>
                                    <ul className="bullet-list">
                                        <li>Điều hành người lao động hoàn thành công việc theo hợp đồng (bố trí, điều chuyển, tạm ngừng việc …)</li>
                                        <li>Tạm hoãn, chấm dứt hợp đồng lao động, kỷ luật người lao động theo quy định của pháp luật, thỏa ước lao động tập thể (nếu có) và nội quy lao động của doanh nghiệp.</li>
                                    </ul>
                                </div>

                                <div className="contract-article">
                                    <p className="contract-article-title">Điều 5: Điều khoản thi hành</p>
                                    <ul className="bullet-list">
                                        <li>Những vấn đề về lao động, không ghi trong hợp đồng lao động này thì áp dụng quy định của thỏa ước lao động tập thể.</li>
                                        <li>Hợp đồng này được làm tại văn phòng Công ty, mỗi bên giữ một bản có giá trị pháp lý như nhau. Và có hiệu lực từ ngày <EditableField value={contractData.contractStartDate} onChange={(value) => updateField('contractStartDate', value)} />.</li>
                                    </ul>
                                </div>

                                <div className="signature-section">
                                    <div className="signature-block">
                                        <p><strong>NGƯỜI LAO ĐỘNG</strong></p>
                                        <p>(Ký tên)</p>
                                        <p>Ghi rõ họ và tên</p>
                                        <p>&nbsp;</p>
                                        <p>&nbsp;</p>
                                        <p><strong><EditableField value={contractData.signatureName} onChange={(value) => updateField('signatureName', value)} /></strong></p>
                                    </div>
                                    <div className="signature-block">
                                        <p><strong>NGƯỜI SỬ DỤNG LAO ĐỘNG</strong></p>
                                        <p>(Ký tên, đóng dấu)</p>
                                        <p>Ghi rõ họ và tên</p>
                                        <p>&nbsp;</p>
                                        <p>&nbsp;</p>
                                        <p><strong>{contractData.employerName.toUpperCase()}</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            );
        };

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>